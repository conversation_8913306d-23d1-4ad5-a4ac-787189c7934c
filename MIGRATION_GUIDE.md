# Database Migration Guide

This guide explains how to run database migrations in your social network application without stopping the server.

## Overview

The application now supports running database migrations in multiple ways:

1. **Automatic migrations on server startup** (existing behavior)
2. **Live migrations via HTTP API** (new feature)
3. **Standalone CLI tool** (new feature)

## Methods to Run Migrations

### 1. HTTP API Endpoints (Recommended for Live Updates)

#### Get Migration Status
```bash
GET /api/migrations/status
```

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "success": true,
  "message": "Migration status retrieved successfully",
  "data": {
    "current_version": 16,
    "is_dirty": false,
    "status": "up_to_date"
  }
}
```

#### Run Migrations
```bash
POST /api/migrations/run
```

**Headers:**
```
Authorization: Bearer <your-jwt-token>
```

**Response:**
```json
{
  "success": true,
  "message": "Migrations completed successfully",
  "data": {
    "success": true,
    "message": "Migrations completed successfully",
    "current_version": 16
  }
}
```

### 2. Using cURL Examples

#### Check migration status:
```bash
curl -X GET http://localhost:8080/api/migrations/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### Run migrations:
```bash
curl -X POST http://localhost:8080/api/migrations/run \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. Standalone CLI Tool

Build and use the migration CLI tool:

```bash
# Build the migration tool
cd backend
go build -o migrate-tool ./cmd/migrate

# Run migrations
./migrate-tool -db ./social_network.db -migrations ./pkg/db/migrations/sqlite -action up

# Check migration status
./migrate-tool -db ./social_network.db -migrations ./pkg/db/migrations/sqlite -action status
```

### 4. Using Docker

If running with Docker Compose, you can execute migrations in the running container:

```bash
# Get migration status
docker-compose exec backend ./migrate-tool -db /app/data/social_network.db -migrations /app/pkg/db/migrations/sqlite -action status

# Run migrations
docker-compose exec backend ./migrate-tool -db /app/data/social_network.db -migrations /app/pkg/db/migrations/sqlite -action up
```

## Security Considerations

- Migration endpoints require authentication (JWT token)
- Only authenticated users can run migrations
- Consider implementing additional authorization checks for admin-only access in production

## Error Handling

The migration system includes proper error handling:

- **Concurrent migrations**: Protected by mutex to prevent concurrent execution
- **Database locks**: Handled gracefully with appropriate error messages
- **Dirty state detection**: Warns when database is in an inconsistent state

## Best Practices

1. **Always check status first**: Use the status endpoint to verify current migration state
2. **Backup before migrations**: Always backup your database before running migrations in production
3. **Test migrations**: Test new migrations in development environment first
4. **Monitor logs**: Check application logs for migration execution details

## Troubleshooting

### Common Issues

1. **"Migration configuration not initialized"**
   - Ensure the server has been started at least once to initialize migration config
   - Or use the standalone CLI tool which initializes config automatically

2. **"Database is dirty"**
   - This indicates a previous migration failed partway through
   - Manual intervention may be required to fix the database state

3. **Permission denied**
   - Ensure you're authenticated and have a valid JWT token
   - Check that the database file has proper write permissions

### Getting Help

If you encounter issues:
1. Check the application logs for detailed error messages
2. Verify database file permissions
3. Ensure migration files are present in the specified directory
4. Test with the standalone CLI tool to isolate API-related issues
