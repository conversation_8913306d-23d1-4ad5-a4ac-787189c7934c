run:
	cd backend && go run .
.PHONY: frontend
frontend:
	cd frontend && npm run dev
format:
	cd backend && gofmt -w -s .
	@echo "files are formatted correctly"
.PHONY: migrate-tool
migrate-tool:
	cd backend && go build -o migrate-tool ./cmd/migrate
	@echo "Migration tool built successfully"
.PHONY: migrate-up
migrate-up:
	cd backend && ./migrate-tool -db ./social_network.db -migrations ./pkg/db/migrations/sqlite -action up
.PHONY: migrate-status
migrate-status:
	cd backend && ./migrate-tool -db ./social_network.db -migrations ./pkg/db/migrations/sqlite -action status
