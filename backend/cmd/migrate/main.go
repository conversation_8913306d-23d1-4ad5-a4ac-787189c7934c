package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/bernaotieno/social-network/backend/pkg/db/sqlite"
)

func main() {
	var (
		dbPath         = flag.String("db", "./social_network.db", "SQLite database path")
		migrationsPath = flag.String("migrations", "./pkg/db/migrations/sqlite", "Path to migrations directory")
		action         = flag.String("action", "up", "Migration action: up, status")
	)
	flag.Parse()

	switch *action {
	case "up":
		fmt.Println("Running migrations...")
		if err := sqlite.RunMigrations(*dbPath, *migrationsPath); err != nil {
			log.Fatalf("Failed to run migrations: %v", err)
		}
		fmt.Println("Migrations completed successfully")

	case "status":
		fmt.Println("Getting migration status...")
		// Initialize configuration first
		sqlite.InitializeMigrationConfig(*dbPath, *migrationsPath)
		
		version, dirty, err := sqlite.GetMigrationStatus()
		if err != nil {
			log.Fatalf("Failed to get migration status: %v", err)
		}
		
		fmt.Printf("Current migration version: %d\n", version)
		fmt.Printf("Database is dirty: %t\n", dirty)
		if dirty {
			fmt.Println("Warning: Database is in a dirty state. Manual intervention may be required.")
		} else {
			fmt.Println("Database is up to date.")
		}

	default:
		fmt.Printf("Unknown action: %s\n", *action)
		fmt.Println("Available actions: up, status")
		os.Exit(1)
	}
}
