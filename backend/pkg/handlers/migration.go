package handlers

import (
	"net/http"

	"github.com/bernaotieno/social-network/backend/pkg/db/sqlite"
	"github.com/bernaotieno/social-network/backend/pkg/middleware"
	"github.com/bernaotieno/social-network/backend/pkg/utils"
)

// MigrationStatusResponse represents the response for migration status
type MigrationStatusResponse struct {
	CurrentVersion uint   `json:"current_version"`
	IsDirty        bool   `json:"is_dirty"`
	Status         string `json:"status"`
}

// MigrationRunResponse represents the response for running migrations
type MigrationRunResponse struct {
	Success        bool   `json:"success"`
	Message        string `json:"message"`
	CurrentVersion uint   `json:"current_version,omitempty"`
}

// GetMigrationStatus handles getting the current migration status
func (h *Handler) GetMigrationStatus(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context to ensure user is authenticated
	_, err := middleware.GetUserID(r)
	if err != nil {
		utils.RespondWithError(w, http.StatusUnauthorized, "Unauthorized")
		return
	}

	// Get migration status
	version, dirty, err := sqlite.GetMigrationStatus()
	if err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, "Failed to get migration status: "+err.Error())
		return
	}

	status := "up_to_date"
	if dirty {
		status = "dirty"
	}

	response := MigrationStatusResponse{
		CurrentVersion: version,
		IsDirty:        dirty,
		Status:         status,
	}

	utils.RespondWithSuccess(w, http.StatusOK, "Migration status retrieved successfully", response)
}

// RunMigrations handles running database migrations while the server is running
func (h *Handler) RunMigrations(w http.ResponseWriter, r *http.Request) {
	// Get user ID from context to ensure user is authenticated
	_, err := middleware.GetUserID(r)
	if err != nil {
		utils.RespondWithError(w, http.StatusUnauthorized, "Unauthorized")
		return
	}

	// Run migrations
	err = sqlite.RunMigrationsLive()
	if err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, "Failed to run migrations: "+err.Error())
		return
	}

	// Get the current version after migration
	version, _, statusErr := sqlite.GetMigrationStatus()

	response := MigrationRunResponse{
		Success: true,
		Message: "Migrations completed successfully",
	}

	if statusErr == nil {
		response.CurrentVersion = version
	}

	utils.RespondWithSuccess(w, http.StatusOK, "Migrations completed successfully", response)
}
