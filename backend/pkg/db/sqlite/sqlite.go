package sqlite

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/sqlite3"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/mattn/go-sqlite3"
)

// Global variables to store migration configuration
var (
	migrationMutex sync.Mutex
	dbPath         string
	migrationsPath string
)

// NewDB creates a new SQLite database connection
func NewDB(dbPath string) (*sql.DB, error) {
	// Ensure the directory exists
	dir := filepath.Dir(dbPath)
	if err := os.MkdirAll(dir, 0o755); err != nil {
		return nil, fmt.Errorf("failed to create database directory: %w", err)
	}

	// Open the database connection
	db, err := sql.Open("sqlite3", dbPath+"?_foreign_keys=on")
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Set connection pool parameters
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(25)

	log.Printf("Connected to SQLite database at %s", dbPath)
	return db, nil
}

// InitializeMigrationConfig stores the migration configuration for later use
func InitializeMigrationConfig(dbPathParam, migrationsPathParam string) {
	migrationMutex.Lock()
	defer migrationMutex.Unlock()
	dbPath = dbPathParam
	migrationsPath = migrationsPathParam
}

// RunMigrations runs all database migrations
func RunMigrations(dbPathParam, migrationsPathParam string) error {
	// Store configuration for later use
	InitializeMigrationConfig(dbPathParam, migrationsPathParam)

	return runMigrations(dbPathParam, migrationsPathParam)
}

// RunMigrationsLive runs migrations using the stored configuration (for live migration endpoint)
func RunMigrationsLive() error {
	migrationMutex.Lock()
	defer migrationMutex.Unlock()

	if dbPath == "" || migrationsPath == "" {
		return fmt.Errorf("migration configuration not initialized")
	}

	return runMigrations(dbPath, migrationsPath)
}

// runMigrations is the internal function that actually runs the migrations
func runMigrations(dbPathParam, migrationsPathParam string) error {
	db, err := sql.Open("sqlite3", dbPathParam)
	if err != nil {
		return fmt.Errorf("failed to open database for migrations: %w", err)
	}
	defer db.Close()

	driver, err := sqlite3.WithInstance(db, &sqlite3.Config{})
	if err != nil {
		return fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Create a new migrate instance
	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", migrationsPathParam),
		"sqlite3",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migration instance: %w", err)
	}

	// Run migrations
	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}

// GetMigrationStatus returns the current migration status
func GetMigrationStatus() (uint, bool, error) {
	migrationMutex.Lock()
	defer migrationMutex.Unlock()

	if dbPath == "" || migrationsPath == "" {
		return 0, false, fmt.Errorf("migration configuration not initialized")
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return 0, false, fmt.Errorf("failed to open database for migration status: %w", err)
	}
	defer db.Close()

	driver, err := sqlite3.WithInstance(db, &sqlite3.Config{})
	if err != nil {
		return 0, false, fmt.Errorf("failed to create migration driver: %w", err)
	}

	// Create a new migrate instance
	m, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", migrationsPath),
		"sqlite3",
		driver,
	)
	if err != nil {
		return 0, false, fmt.Errorf("failed to create migration instance: %w", err)
	}

	version, dirty, err := m.Version()
	if err != nil {
		return 0, false, fmt.Errorf("failed to get migration version: %w", err)
	}

	return version, dirty, nil
}
